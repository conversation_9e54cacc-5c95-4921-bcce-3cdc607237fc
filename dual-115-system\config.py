#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双115系统配置文件
"""

import os
from pathlib import Path

# 115账号配置
MAIN_115_COOKIES = os.getenv('MAIN_115_COOKIES', '~/main-115-cookies.txt')
SUB_115_COOKIES = os.getenv('SUB_115_COOKIES', '~/sub-115-cookies.txt')

# 数据库配置
DB_PATH = os.getenv('DB_PATH', 'dual_115_system.db')

# 主115扫描目录配置（目录ID列表）
MAIN_SCAN_DIRS = [d.strip() for d in os.getenv('MAIN_SCAN_DIRS', '').split(',') if d.strip()]

# 副115缓存目录ID
SUB_CACHE_DIR = os.getenv('SUB_CACHE_DIR', '')

# 更新间隔（秒）
UPDATE_INTERVAL = int(os.getenv('UPDATE_INTERVAL', '3600'))  # 默认1小时

# Emby服务器配置
EMBY_SERVER = os.getenv('EMBY_SERVER', 'http://**********:28096')

# Flask应用配置
FLASK_HOST = os.getenv('FLASK_HOST', '0.0.0.0')
FLASK_PORT = int(os.getenv('FLASK_PORT', '40097'))
FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

# 日志配置
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
LOG_FILE = os.getenv('LOG_FILE', 'dual_115_system.log')

# 文件类型过滤（只处理视频文件）
VIDEO_EXTENSIONS = {
    '.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v',
    '.mpg', '.mpeg', '.3gp', '.f4v', '.asf', '.rm', '.rmvb', '.ts',
    '.m2ts', '.mts', '.vob', '.iso'
}

# 音频文件扩展名
AUDIO_EXTENSIONS = {
    '.mp3', '.flac', '.wav', '.aac', '.ogg', '.wma', '.m4a', '.ape',
    '.dts', '.ac3', '.opus', '.aiff'
}

# 所有媒体文件扩展名
MEDIA_EXTENSIONS = VIDEO_EXTENSIONS | AUDIO_EXTENSIONS

# 文件查找策略配置
FILE_SEARCH_STRATEGY = os.getenv('FILE_SEARCH_STRATEGY', 'hybrid')  # database, realtime, hybrid
ENABLE_REALTIME_FALLBACK = os.getenv('ENABLE_REALTIME_FALLBACK', 'true').lower() == 'true'
VERIFY_SUB_FILE_EXISTS = os.getenv('VERIFY_SUB_FILE_EXISTS', 'true').lower() == 'true'

# 缓存配置
DATABASE_CACHE_TTL = int(os.getenv('DATABASE_CACHE_TTL', '86400'))  # 24小时
REALTIME_SEARCH_TIMEOUT = int(os.getenv('REALTIME_SEARCH_TIMEOUT', '10'))  # 10秒


def get_config():
    """获取完整配置字典"""
    return {
        'main_115_cookies': MAIN_115_COOKIES,
        'sub_115_cookies': SUB_115_COOKIES,
        'db_path': DB_PATH,
        'main_scan_dirs': MAIN_SCAN_DIRS,
        'sub_cache_dir': SUB_CACHE_DIR,
        'update_interval': UPDATE_INTERVAL,
        'emby_server': EMBY_SERVER,
        'flask_host': FLASK_HOST,
        'flask_port': FLASK_PORT,
        'flask_debug': FLASK_DEBUG,
        'log_level': LOG_LEVEL,
        'log_file': LOG_FILE,
        'video_extensions': VIDEO_EXTENSIONS,
        'audio_extensions': AUDIO_EXTENSIONS,
        'media_extensions': MEDIA_EXTENSIONS,
        'file_search_strategy': FILE_SEARCH_STRATEGY,
        'enable_realtime_fallback': ENABLE_REALTIME_FALLBACK,
        'verify_sub_file_exists': VERIFY_SUB_FILE_EXISTS,
        'database_cache_ttl': DATABASE_CACHE_TTL,
        'realtime_search_timeout': REALTIME_SEARCH_TIMEOUT,
    }


def validate_config():
    """验证配置"""
    errors = []
    
    # 检查cookies文件
    main_cookies_path = Path(MAIN_115_COOKIES).expanduser()
    if not main_cookies_path.exists():
        errors.append(f"主115 cookies文件不存在: {main_cookies_path}")
    
    sub_cookies_path = Path(SUB_115_COOKIES).expanduser()
    if not sub_cookies_path.exists():
        errors.append(f"副115 cookies文件不存在: {sub_cookies_path}")
    
    # 检查目录ID格式
    if not MAIN_SCAN_DIRS:
        errors.append("至少需要配置一个主115扫描目录ID")

    for dir_id in MAIN_SCAN_DIRS:
        if not dir_id.strip() or not dir_id.isdigit():
            errors.append(f"扫描目录ID格式错误: {dir_id}")

    if not SUB_CACHE_DIR.strip():
        errors.append("副115缓存目录ID不能为空")
    elif not SUB_CACHE_DIR.isdigit():
        errors.append(f"副115缓存目录ID格式错误: {SUB_CACHE_DIR}")
    
    # 检查端口
    if not (40000 <= FLASK_PORT <= 49999):
        errors.append(f"Flask端口号应为4开头的5位数: {FLASK_PORT}")
    
    return errors


if __name__ == '__main__':
    # 验证配置
    errors = validate_config()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("配置验证通过")
        
    # 打印当前配置
    config = get_config()
    print("\n当前配置:")
    for key, value in config.items():
        if 'cookies' in key:
            print(f"  {key}: {value} ({'存在' if Path(value).expanduser().exists() else '不存在'})")
        else:
            print(f"  {key}: {value}")
