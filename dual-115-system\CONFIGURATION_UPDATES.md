# 双115系统配置更新说明

## 🔄 主要更新内容

根据用户需求，已对双115系统进行以下重要更新：

### 1. Python版本升级
- **更新前**: Python 3.11
- **更新后**: Python 3.13
- **原因**: p115client需要Python 3.12/3.13版本支持

### 2. STRM内容获取优化
- **更新前**: 需要挂载Emby媒体库目录读取STRM文件
- **更新后**: 直接通过Emby API获取STRM内容
- **优势**: 
  - 无需挂载媒体库目录
  - 支持多种STRM内容获取方式
  - 更好的容器化部署体验

### 3. 扫描目录配置优化
- **更新前**: 默认包含根目录0
- **更新后**: 移除根目录0，只扫描指定目录
- **配置示例**: `MAIN_SCAN_DIRS=123456789,987654321`
- **注意**: 必须配置具体的目录ID，不能为空

### 4. Emby服务器地址更新
- **更新前**: `http://localhost:8096`
- **更新后**: `http://**********:28096`
- **说明**: 适配用户的实际Emby服务器地址

### 5. 端口配置标准化
- **更新前**: 使用4位端口号
- **更新后**: 统一使用4开头的5位端口号
- **端口映射**:
  - 双115系统API: `40097`
  - Nginx代理: `40096`
  - 健康检查: `40097`

## 📋 新的配置要求

### 必需配置项

```bash
# 115账号配置
MAIN_115_COOKIES=/app/cookies/main-115-cookies.txt
SUB_115_COOKIES=/app/cookies/sub-115-cookies.txt

# 扫描配置（必须指定具体目录ID）
MAIN_SCAN_DIRS=123456789,987654321  # 不能包含0
SUB_CACHE_DIR=111111111

# Emby服务器配置
EMBY_SERVER=http://**********:28096

# 端口配置（4开头5位数）
FLASK_PORT=40097
```

### 配置验证规则

1. **扫描目录ID**: 必须是数字，不能为空，不能包含根目录0
2. **缓存目录ID**: 必须是数字，不能为空
3. **端口号**: 必须是40000-49999范围内的5位数
4. **Cookies文件**: 必须存在且有效

## 🔧 STRM内容获取机制

新的STRM内容获取采用三层策略：

### 方法1: 媒体源直接获取
```python
# 从MediaSources中获取HTTP协议的流媒体URL
if source.get('Protocol') == 'Http':
    stream_url = source.get('Path', '')
```

### 方法2: 文件路径读取
```python
# 如果有STRM文件路径，直接读取内容
if strm_path.lower().endswith('.strm'):
    with open(strm_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
```

### 方法3: API获取播放信息
```python
# 通过Emby PlaybackInfo API获取媒体流信息
api_url = f"{EMBY_SERVER}/emby/Items/{media_id}/PlaybackInfo"
```

## 🚀 部署步骤更新

### 1. 环境准备
```bash
# 确保Docker和Docker Compose已安装
docker --version
docker-compose --version

# Python 3.13环境（如果本地测试）
python --version  # 应显示3.13.x
```

### 2. 配置文件准备
```bash
# 创建目录
mkdir -p cookies data logs

# 复制cookies文件
cp main-115-cookies.txt cookies/
cp sub-115-cookies.txt cookies/

# 编辑配置
cp .env.example .env
# 修改.env中的配置项
```

### 3. 启动服务
```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f dual-115-system

# 健康检查
curl http://localhost:40097/health
```

## 🔍 验证配置

### 配置验证命令
```bash
# 验证配置文件
python config.py

# 检查端口监听
netstat -tlnp | grep 40097

# 测试API接口
curl http://localhost:40097/api/stats
```

### 常见配置错误

1. **扫描目录包含0**
   ```bash
   # 错误
   MAIN_SCAN_DIRS=0,123456789
   
   # 正确
   MAIN_SCAN_DIRS=123456789,987654321
   ```

2. **端口号格式错误**
   ```bash
   # 错误
   FLASK_PORT=8097
   
   # 正确
   FLASK_PORT=40097
   ```

3. **Emby地址错误**
   ```bash
   # 错误
   EMBY_SERVER=http://localhost:8096
   
   # 正确
   EMBY_SERVER=http://**********:28096
   ```

## 📊 性能优化建议

### 1. 扫描策略优化
```bash
# 高性能场景
FILE_SEARCH_STRATEGY=database
ENABLE_REALTIME_FALLBACK=false

# 平衡场景（推荐）
FILE_SEARCH_STRATEGY=hybrid
ENABLE_REALTIME_FALLBACK=true

# 高准确性场景
FILE_SEARCH_STRATEGY=realtime
```

### 2. 缓存配置优化
```bash
# 数据库缓存时间（24小时）
DATABASE_CACHE_TTL=86400

# 实时搜索超时（10秒）
REALTIME_SEARCH_TIMEOUT=10

# 扫描间隔（1小时）
UPDATE_INTERVAL=3600
```

## 🛠️ 故障排除

### 1. Python版本问题
```bash
# 检查Python版本
python --version

# 如果版本不对，更新Dockerfile
FROM python:3.13-slim
```

### 2. 端口冲突
```bash
# 检查端口占用
netstat -tlnp | grep 40097

# 修改端口配置
FLASK_PORT=40098
```

### 3. STRM内容获取失败
```bash
# 检查Emby连接
curl http://**********:28096/emby/System/Info

# 检查API权限
# 确保API密钥或用户认证正确
```

### 4. 目录ID配置错误
```bash
# 获取正确的目录ID
# 1. 登录115网页版
# 2. 进入目录
# 3. 查看URL: https://115.com/?cid=123456789
# 4. 使用123456789作为目录ID
```

## 📝 迁移指南

如果从旧版本升级，请按以下步骤操作：

### 1. 备份数据
```bash
cp -r data data.backup
cp .env .env.backup
```

### 2. 更新配置
```bash
# 更新.env文件
sed -i 's/FLASK_PORT=8097/FLASK_PORT=40097/g' .env
sed -i 's/EMBY_SERVER=http:\/\/localhost:8096/EMBY_SERVER=http:\/\/**********:28096/g' .env
sed -i 's/MAIN_SCAN_DIRS=0,/MAIN_SCAN_DIRS=/g' .env
```

### 3. 重新部署
```bash
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### 4. 验证升级
```bash
curl http://localhost:40097/health
curl http://localhost:40097/api/stats
```

这些更新确保了双115系统能够更好地适配用户的实际环境，提供更稳定和高效的服务。
