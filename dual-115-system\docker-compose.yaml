version: '3.8'

services:
  dual-115-system:
    build:
      context: ..
      dockerfile: dual-115-system/Dockerfile
    container_name: dual-115-system
    restart: unless-stopped
    ports:
      - "40097:40097"
    volumes:
      # 115 cookies文件
      - ./cookies:/app/cookies:ro
      # 数据库持久化
      - ./data:/app/data
      # 日志目录
      - ./logs:/app/logs
    environment:
      # 115账号配置
      - MAIN_115_COOKIES=/app/cookies/main-115-cookies.txt
      - SUB_115_COOKIES=/app/cookies/sub-115-cookies.txt
      
      # 数据库配置
      - DB_PATH=/app/data/dual_115_system.db
      
      # 扫描配置
      - MAIN_SCAN_DIRS=123456789,987654321  # 主115扫描目录ID，多个用逗号分隔
      - SUB_CACHE_DIR=111111111     # 副115缓存目录ID
      - UPDATE_INTERVAL=3600        # 更新间隔（秒）
      
      # Emby服务器配置
      - EMBY_SERVER=http://**********:28096  # Emby服务器地址
      
      # Flask应用配置
      - FLASK_HOST=0.0.0.0
      - FLASK_PORT=40097
      - FLASK_DEBUG=false
      
      # 日志配置
      - LOG_LEVEL=INFO
      - LOG_FILE=/app/logs/dual_115_system.log
      
      # 文件查找策略配置
      - FILE_SEARCH_STRATEGY=hybrid
      - ENABLE_REALTIME_FALLBACK=true
      - VERIFY_SUB_FILE_EXISTS=true
      - DATABASE_CACHE_TTL=86400
      - REALTIME_SEARCH_TIMEOUT=10
    
    networks:
      - dual-115-network

  # Nginx反向代理（可选，如果需要代理Emby请求）
  nginx:
    image: nginx:alpine
    container_name: dual-115-nginx
    restart: unless-stopped
    ports:
      - "40096:80"  # 将40096端口映射到nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - dual-115-system
    networks:
      - dual-115-network

networks:
  dual-115-network:
    driver: bridge
